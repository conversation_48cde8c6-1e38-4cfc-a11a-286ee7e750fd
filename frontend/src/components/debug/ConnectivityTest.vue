<template>
  <div class="connectivity-test p-4 bg-card rounded-lg border">
    <h3 class="text-lg font-semibold mb-4">🔌 Backend Connectivity Test</h3>
    
    <!-- Connection Status -->
    <div class="mb-4">
      <div class="flex items-center gap-2 mb-2">
        <div 
          :class="[
            'w-3 h-3 rounded-full',
            connectionStatus === 'connected' ? 'bg-green-500' : 
            connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
          ]"
        />
        <span class="font-medium">
          {{ connectionStatus === 'connected' ? 'Connected' : 
             connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected' }}
        </span>
      </div>
      <p class="text-sm text-muted-foreground">
        Session ID: {{ sessionId }}
      </p>
    </div>

    <!-- Service Health -->
    <div class="mb-4">
      <h4 class="font-medium mb-2">Service Health</h4>
      <div class="grid grid-cols-2 gap-2 text-sm">
        <div class="flex items-center gap-2">
          <div :class="['w-2 h-2 rounded-full', healthStatus.websocket ? 'bg-green-500' : 'bg-red-500']" />
          WebSocket
        </div>
        <div class="flex items-center gap-2">
          <div :class="['w-2 h-2 rounded-full', healthStatus.agents ? 'bg-green-500' : 'bg-red-500']" />
          Agents
        </div>
        <div class="flex items-center gap-2">
          <div :class="['w-2 h-2 rounded-full', healthStatus.voice ? 'bg-green-500' : 'bg-red-500']" />
          Voice
        </div>
        <div class="flex items-center gap-2">
          <div :class="['w-2 h-2 rounded-full', healthStatus.database ? 'bg-green-500' : 'bg-red-500']" />
          Database
        </div>
      </div>
    </div>

    <!-- Test Controls -->
    <div class="space-y-2">
      <div class="flex gap-2">
        <Button 
          @click="testConnection" 
          :disabled="testing"
          size="sm"
        >
          {{ testing ? 'Testing...' : 'Test Connection' }}
        </Button>
        <Button 
          @click="sendTestMessage" 
          :disabled="!isConnected || testing"
          size="sm"
          variant="outline"
        >
          Send Test Message
        </Button>
      </div>
      
      <div class="flex gap-2">
        <Button 
          @click="testAgentService" 
          :disabled="testing"
          size="sm"
          variant="outline"
        >
          Test Agent Service
        </Button>
        <Button 
          @click="testVoiceService" 
          :disabled="testing"
          size="sm"
          variant="outline"
        >
          Test Voice Service
        </Button>
      </div>
    </div>

    <!-- Test Results -->
    <div v-if="testResults.length > 0" class="mt-4">
      <h4 class="font-medium mb-2">Test Results</h4>
      <div class="bg-background rounded p-3 max-h-40 overflow-y-auto text-sm font-mono">
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          :class="[
            'mb-1',
            result.type === 'error' ? 'text-red-400' : 
            result.type === 'success' ? 'text-green-400' : 'text-blue-400'
          ]"
        >
          [{{ result.timestamp }}] {{ result.message }}
        </div>
      </div>
      <Button @click="clearResults" size="sm" variant="ghost" class="mt-2">
        Clear Results
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { useAgentium } from '@/composables/useAgentium'
import { getApiUrl, getAgentServiceUrl, getVoiceServiceUrl } from '@/lib/utils'
import Button from '@/components/ui/Button.vue'

interface TestResult {
  timestamp: string
  message: string
  type: 'info' | 'success' | 'error'
}

const store = useAppStore()
const agentium = useAgentium()

const testing = ref(false)
const testResults = ref<TestResult[]>([])

const connectionStatus = computed(() => {
  if (store.isConnected) return 'connected'
  if (store.reconnectAttempts > 0) return 'connecting'
  return 'disconnected'
})

const isConnected = computed(() => store.isConnected)
const sessionId = computed(() => store.sessionId || 'Not set')

const healthStatus = computed(() => ({
  websocket: store.isConnected,
  agents: store.systemHealth.services.agents,
  voice: store.systemHealth.services.voice,
  database: store.systemHealth.services.database
}))

const log = (message: string, type: TestResult['type'] = 'info') => {
  testResults.value.push({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type
  })
}

const testConnection = async () => {
  testing.value = true
  log('Starting connection test...', 'info')
  
  try {
    // Test WebSocket connection
    if (!agentium.isConnected.value) {
      log('Attempting to connect to WebSocket...', 'info')
      const connected = await agentium.initialize()
      if (connected) {
        log('WebSocket connection successful!', 'success')
      } else {
        log('WebSocket connection failed', 'error')
      }
    } else {
      log('WebSocket already connected', 'success')
    }
    
    // Test health endpoints
    await testHealthEndpoints()
    
  } catch (error) {
    log(`Connection test failed: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

const testHealthEndpoints = async () => {
  const endpoints = [
    { name: 'FastAPI WebSocket', url: getApiUrl('/health') },
    { name: 'Agent Service', url: getAgentServiceUrl('/health') },
    { name: 'Voice Service', url: getVoiceServiceUrl('/health') }
  ]

  for (const endpoint of endpoints) {
    try {
      log(`Testing ${endpoint.name}...`, 'info')
      const response = await fetch(endpoint.url)
      const data = await response.json()
      
      if (response.ok && data.status === 'healthy') {
        log(`${endpoint.name}: Healthy ✅`, 'success')
      } else {
        log(`${endpoint.name}: Unhealthy ❌`, 'error')
      }
    } catch (error) {
      log(`${endpoint.name}: Error - ${error.message}`, 'error')
    }
  }
}

const sendTestMessage = async () => {
  if (!agentium.isConnected.value) {
    log('Cannot send message: Not connected', 'error')
    return
  }

  testing.value = true
  log('Sending test message...', 'info')
  
  try {
    await agentium.sendMessage('Hello! This is a connectivity test message.')
    log('Test message sent successfully!', 'success')
  } catch (error) {
    log(`Failed to send test message: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

const testAgentService = async () => {
  testing.value = true
  log('Testing Agent Service directly...', 'info')
  
  try {
    const response = await fetch(getAgentServiceUrl('/tasks/process'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: 'Hello! This is a direct test of the agent service.',
        session_id: store.sessionId || 'test-session',
        priority: 'medium'
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    log('Agent Service test successful!', 'success')
    log(`Agent: ${result.agent_id}`, 'info')
    log(`Response: ${result.result.substring(0, 50)}...`, 'info')
    log(`Cost: $${result.cost}`, 'info')
    
  } catch (error) {
    log(`Agent Service test failed: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

const testVoiceService = async () => {
  testing.value = true
  log('Testing Voice Service...', 'info')
  
  try {
    const response = await fetch(getVoiceServiceUrl('/health'))
    const data = await response.json()
    
    if (response.ok && data.status === 'healthy') {
      log('Voice Service: Healthy ✅', 'success')
      log(`Providers: ${Object.keys(data.providers || {}).join(', ')}`, 'info')
    } else {
      log('Voice Service: Unhealthy ❌', 'error')
    }
  } catch (error) {
    log(`Voice Service test failed: ${error.message}`, 'error')
  } finally {
    testing.value = false
  }
}

const clearResults = () => {
  testResults.value = []
}

onMounted(() => {
  log('Connectivity test component loaded', 'info')
  log(`Frontend URL: ${window.location.origin}`, 'info')
  log(`WebSocket URL: ws://localhost:8000/ws/{session_id}`, 'info')
})
</script>

<style scoped>
.connectivity-test {
  font-family: system-ui, -apple-system, sans-serif;
}
</style>
