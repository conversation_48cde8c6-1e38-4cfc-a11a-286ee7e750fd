#!/usr/bin/env node

/**
 * Simple connectivity test for Agentium backend services
 * Tests WebSocket connection and basic message flow
 */

const WebSocket = require('ws');

// Configuration
const WS_URL = 'ws://localhost:8000/ws/test-session-123';
const AGENT_SERVICE_URL = 'http://localhost:8001';
const VOICE_SERVICE_URL = 'http://localhost:8002';

// Test HTTP endpoints first
async function testHttpEndpoints() {
  console.log('🔍 Testing HTTP endpoints...\n');
  
  const endpoints = [
    { name: 'FastAPI WebSocket Service', url: 'http://localhost:8000/health' },
    { name: 'Agent Service', url: 'http://localhost:8001/health' },
    { name: 'Voice Service', url: 'http://localhost:8002/health' }
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint.url);
      const data = await response.json();
      console.log(`✅ ${endpoint.name}: ${data.status}`);
      if (data.agents) {
        console.log(`   Available agents: ${data.agents.join(', ')}`);
      }
      if (data.providers) {
        console.log(`   Voice providers: ${Object.keys(data.providers).join(', ')}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.message}`);
    }
  }
  console.log('');
}

// Test WebSocket connection
async function testWebSocketConnection() {
  console.log('🔌 Testing WebSocket connection...\n');
  
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(WS_URL);
    let connected = false;
    
    const timeout = setTimeout(() => {
      if (!connected) {
        ws.close();
        reject(new Error('WebSocket connection timeout'));
      }
    }, 10000);

    ws.on('open', () => {
      connected = true;
      clearTimeout(timeout);
      console.log('✅ WebSocket connected successfully');
      
      // Send a test message
      const testMessage = {
        type: 'text_input',
        data: {
          message: 'Hello! This is a connectivity test.',
          context: { test: true }
        },
        timestamp: new Date().toISOString(),
        session_id: 'test-session-123'
      };
      
      console.log('📤 Sending test message...');
      ws.send(JSON.stringify(testMessage));
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log(`📥 Received message: ${message.type}`);
        
        if (message.type === 'connection_status') {
          console.log(`   Status: ${message.data.status}`);
          console.log(`   Session ID: ${message.data.session_id}`);
        } else if (message.type === 'agent_response') {
          console.log(`   Agent: ${message.data.agent_id}`);
          console.log(`   Response: ${message.data.message.substring(0, 100)}...`);
          console.log(`   Cost: $${message.data.metadata?.cost || 0}`);
          console.log(`   Tokens: ${message.data.metadata?.tokens_used || 0}`);
          
          // Test successful - close connection
          setTimeout(() => {
            ws.close();
            resolve(true);
          }, 1000);
        } else if (message.type === 'error') {
          console.log(`❌ Error: ${message.data.message}`);
          ws.close();
          reject(new Error(message.data.message));
        }
      } catch (error) {
        console.log(`❌ Failed to parse message: ${error.message}`);
      }
    });

    ws.on('error', (error) => {
      console.log(`❌ WebSocket error: ${error.message}`);
      reject(error);
    });

    ws.on('close', (code, reason) => {
      console.log(`🔌 WebSocket closed: ${code} ${reason}`);
      if (!connected) {
        reject(new Error(`WebSocket closed before connecting: ${code} ${reason}`));
      }
    });
  });
}

// Test agent service directly
async function testAgentService() {
  console.log('🤖 Testing Agent Service directly...\n');
  
  try {
    const response = await fetch(`${AGENT_SERVICE_URL}/tasks/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: 'Hello! This is a direct test of the agent service.',
        session_id: 'test-session-direct',
        priority: 'medium'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Agent Service direct test successful');
    console.log(`   Task ID: ${result.task_id}`);
    console.log(`   Agent: ${result.agent_id}`);
    console.log(`   Response: ${result.result.substring(0, 100)}...`);
    console.log(`   Cost: $${result.cost}`);
    console.log(`   Processing time: ${result.processing_time_ms}ms`);
    
  } catch (error) {
    console.log(`❌ Agent Service direct test failed: ${error.message}`);
  }
  console.log('');
}

// Main test function
async function runConnectivityTests() {
  console.log('🚀 Agentium Backend Connectivity Test\n');
  console.log('=' .repeat(50));
  
  try {
    // Test HTTP endpoints
    await testHttpEndpoints();
    
    // Test Agent Service directly
    await testAgentService();
    
    // Test WebSocket connection
    await testWebSocketConnection();
    
    console.log('🎉 All connectivity tests passed!');
    console.log('\n✅ Your backend is ready for frontend integration.');
    
  } catch (error) {
    console.log(`\n❌ Connectivity test failed: ${error.message}`);
    console.log('\n🔧 Please check your backend services and try again.');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runConnectivityTests();
}

module.exports = { testHttpEndpoints, testWebSocketConnection, testAgentService };
