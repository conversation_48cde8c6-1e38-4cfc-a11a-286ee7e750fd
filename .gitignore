# MCP Configuration (contains secrets)
.roo/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
test_env/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore

# Data directories
data/docs/*
!data/docs/.gitkeep

# Cache directories
cache/
mcp_tools/
backups/

# Temporary files
*.tmp
*.temp
test_*.json
test_results.json

# Audio files
*.wav
*.mp3
*.flac
*.ogg

# Model files
*.bin
*.safetensors
*.onnx

# Database
*.db
*.sqlite
*.sqlite3

# Secrets and keys
*.pem
*.key
*.crt
secrets/

# Runtime files
*.pid
*.lock