<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentium WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected { background: #0f5132; }
        .status.disconnected { background: #842029; }
        .status.connecting { background: #664d03; }
        button {
            background: #0d6efd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0b5ed7; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #messages {
            background: #000;
            padding: 15px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input[type="text"] {
            width: 70%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #fff;
        }
    </style>
</head>
<body>
    <h1>🚀 Agentium WebSocket Connectivity Test</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <div id="status" class="status disconnected">Disconnected</div>
        <button id="connectBtn" onclick="connect()">Connect</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
    </div>

    <div class="container">
        <h2>Send Test Message</h2>
        <input type="text" id="messageInput" placeholder="Enter test message..." value="Hello! This is a test message.">
        <button id="sendBtn" onclick="sendMessage()" disabled>Send Message</button>
    </div>

    <div class="container">
        <h2>Messages Log</h2>
        <div id="messages"></div>
        <button onclick="clearMessages()">Clear Log</button>
    </div>

    <script>
        let ws = null;
        const sessionId = 'test-session-' + Date.now();
        
        function log(message, type = 'info') {
            const messages = document.getElementById('messages');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            messages.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            messages.scrollTop = messages.scrollHeight;
        }

        function updateStatus(status) {
            const statusEl = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            
            statusEl.className = `status ${status}`;
            
            switch(status) {
                case 'connected':
                    statusEl.textContent = 'Connected ✅';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    sendBtn.disabled = false;
                    break;
                case 'connecting':
                    statusEl.textContent = 'Connecting... ⏳';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = true;
                    sendBtn.disabled = true;
                    break;
                case 'disconnected':
                    statusEl.textContent = 'Disconnected ❌';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    sendBtn.disabled = true;
                    break;
            }
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected!', 'error');
                return;
            }

            updateStatus('connecting');
            log('Attempting to connect to WebSocket...');
            
            const wsUrl = `ws://localhost:8000/ws/${sessionId}`;
            log(`WebSocket URL: ${wsUrl}`);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                log('WebSocket connected successfully!', 'success');
                updateStatus('connected');
            };
            
            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    log(`📥 Received: ${message.type}`, 'success');
                    
                    if (message.type === 'connection_status') {
                        log(`   Status: ${message.data.status}`);
                        log(`   Session ID: ${message.data.session_id}`);
                    } else if (message.type === 'agent_response') {
                        log(`   Agent: ${message.data.agent_id}`);
                        log(`   Message: ${message.data.message.substring(0, 100)}...`);
                        if (message.data.metadata) {
                            log(`   Cost: $${message.data.metadata.cost || 0}`);
                            log(`   Tokens: ${message.data.metadata.tokens_used || 0}`);
                        }
                    } else if (message.type === 'error') {
                        log(`   Error: ${message.data.message}`, 'error');
                    } else {
                        log(`   Data: ${JSON.stringify(message.data).substring(0, 200)}...`);
                    }
                } catch (error) {
                    log(`Failed to parse message: ${error.message}`, 'error');
                    log(`Raw message: ${event.data}`);
                }
            };
            
            ws.onerror = function(error) {
                log(`WebSocket error: ${error.message || 'Unknown error'}`, 'error');
            };
            
            ws.onclose = function(event) {
                log(`WebSocket closed: ${event.code} ${event.reason}`, 'error');
                updateStatus('disconnected');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateStatus('disconnected');
            log('Disconnected from WebSocket');
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('Not connected to WebSocket!', 'error');
                return;
            }

            const messageInput = document.getElementById('messageInput');
            const text = messageInput.value.trim();
            
            if (!text) {
                log('Please enter a message!', 'error');
                return;
            }

            const message = {
                type: 'text_input',
                data: {
                    message: text,
                    context: { test: true }
                },
                timestamp: new Date().toISOString(),
                session_id: sessionId
            };

            try {
                ws.send(JSON.stringify(message));
                log(`📤 Sent: ${text}`, 'success');
                messageInput.value = '';
            } catch (error) {
                log(`Failed to send message: ${error.message}`, 'error');
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Auto-connect on page load
        window.onload = function() {
            log('WebSocket test page loaded');
            log(`Session ID: ${sessionId}`);
            setTimeout(connect, 1000);
        };
    </script>
</body>
</html>
