{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/start_system.sh:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "<PERSON><PERSON>(source:*)", "Bash(docker kill:*)", "Bash(grep:*)", "Bash(cp:*)", "Bash(echo)", "Bash(git init:*)", "Bash(git config:*)", "Bash(git branch:*)", "Bash(git add:*)", "Bash(git remote add:*)", "Bash(git push:*)", "Bash(git remote set-url:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(find:*)", "Bash(rm:*)"], "deny": []}}